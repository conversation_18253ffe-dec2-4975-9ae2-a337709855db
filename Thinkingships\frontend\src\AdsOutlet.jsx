import React from "react";
import { useEffect } from "react";
import { Outlet } from "react-router-dom";

function AdsOutletComponent() {
  useEffect(()=>{
    console.log("ads mounting check")
    return ()=> console.log("ads unmounting")
  },[])
  return (
    <div className="flex flex-1">
  <div
    className="hidden md:fixed md:flex right-0 top-0 w-[250px] overflow-y-scroll bg-white"
    style={{ height: "calc(100% - 64.67px)",top: "64.67px"  }}
  >
    {/* Ad content */}
    <p className="p-4 text-gray-600 text-sm">Ad space</p>
  </div>
{/* flex-1 md:mt-10 mt-20 py-6 pl-6 pr-6 md:pr-0 min-h-screen md:mb-0 mb-20 max-w-screen overflow-x-hidden */}
  <div className="md:mr-[250px] md:pr-6 pr-0 flex-1 md:mt-8 w-full">
    <Outlet />
  </div>
</div>
  );
}
const AdsOutlet = React.memo(AdsOutletComponent);
export default AdsOutlet;
