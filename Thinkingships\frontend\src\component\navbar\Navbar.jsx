import { SquarePen } from "lucide-react";
import { useNavigate } from "react-router-dom";

function Navbar() {
    const navigate = useNavigate()
    return (
        <div className="navbar fixed top-0 left-0 right-0 z-50 bg-white shadow-lg px-3 sm:px-4 lg:px-6 py-2 sm:py-3 border-b border-gray-200 pl-20 sm:pl-24 md:pl-44 lg:pl-48">
            <div className="flex items-center justify-between w-full">
                {/* Left Nav Links */}
                <ul className="hidden sm:flex gap-3 md:gap-6 lg:gap-8 text-xs sm:text-sm font-semibold text-gray-600">
                    <li>
                        <a
                            className="hover:text-[#4A99F8] transition-all duration-300 cursor-pointer hover:scale-105 relative group px-1 sm:px-2 py-1 rounded-md hover:bg-blue-50"
                            onClick={()=>navigate("/romance")}
                        >
                            <span className="hidden md:inline">Romance</span>
                            <span className="md:hidden">Rom</span>
                            <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-[#4A99F8] transition-all duration-300 group-hover:w-full"></span>
                        </a>
                    </li>
                    <li>
                        <a
                            className="hover:text-[#4A99F8] transition-all duration-300 cursor-pointer hover:scale-105 relative group px-1 sm:px-2 py-1 rounded-md hover:bg-blue-50"
                            onClick={()=>navigate("/fantasy")}
                        >
                            <span className="hidden md:inline">Fantasy</span>
                            <span className="md:hidden">Fan</span>
                            <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-[#4A99F8] transition-all duration-300 group-hover:w-full"></span>
                        </a>
                    </li>
                    <li>
                        <a
                            className="hover:text-[#4A99F8] transition-all duration-300 cursor-pointer hover:scale-105 relative group px-1 sm:px-2 py-1 rounded-md hover:bg-blue-50"
                            onClick={()=>navigate("/mystery")}
                        >
                            <span className="hidden md:inline">Mystery</span>
                            <span className="md:hidden">Mys</span>
                            <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-[#4A99F8] transition-all duration-300 group-hover:w-full"></span>
                        </a>
                    </li>
                    <li>
                        <a
                            className="hover:text-[#4A99F8] transition-all duration-300 cursor-pointer hover:scale-105 relative group px-1 sm:px-2 py-1 rounded-md hover:bg-blue-50"
                            onClick={()=>navigate("/all")}
                        >
                            <span className="hidden lg:inline">Browse All</span>
                            <span className="lg:hidden">All</span>
                            <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-[#4A99F8] transition-all duration-300 group-hover:w-full"></span>
                        </a>
                    </li>
                </ul>

                {/* Mobile Menu Button */}
                <div className="sm:hidden">
                    <button className="p-2 rounded-md text-gray-600 hover:text-[#4A99F8] hover:bg-blue-50 transition-all duration-300">
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 6h16M4 12h16M4 18h16" />
                        </svg>
                    </button>
                </div>

                {/* Center Logo / Branding */}
                <div className="flex items-center gap-1 sm:gap-2 text-[#4A99F8]">
                    <SquarePen
                        width={20}
                        height={20}
                        onClick={()=>navigate("/skrivee")}
                        className="sm:w-6 sm:h-6 cursor-pointer transition-all duration-300 hover:scale-110 hover:rotate-12 hover:text-[#3B82F6] drop-shadow-md"
                    />
                    <span
                        className="font-bold text-sm sm:text-base lg:text-lg tracking-wide cursor-pointer transition-all duration-300 hover:scale-105 hover:text-[#3B82F6] drop-shadow-sm"
                        onClick={()=>navigate("/skrivee")}
                    >
                        Skrivee
                    </span>
                </div>

                {/* Right side - empty for balance */}
                <div className="w-8 sm:w-16 md:w-32 lg:w-64"></div>
            </div>
        </div>
    );
}

export default Navbar;
