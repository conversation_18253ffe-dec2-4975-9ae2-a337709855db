import React, { useState } from 'react'
import { Search, Menu, X, User } from 'lucide-react'
import logo from '/logo/Logo.svg'

function Authors() {
  const [formData, setFormData] = useState({
    userName: '<PERSON>_<PERSON>',
    fullName: '<PERSON>',
    bio: 'Lorem ipsum dolor sit amet consectetur. Odio pulvinar eleifend quis aliquet congue velit donec. Pellentesque sagittis in nunc tristique risus volutpat.',
    passion: 'Connecting with Readers, Continuous Learning Inspiring Others',
    gender: 'male',
    customGender: '',
    dob: '1991-02-04',
    occupation: 'Student',
    location: '',
    phone: '',
    language: '',
    social_web_url: '',
    social_insta_url: '',
    social_fb_url: '',
    social_linkedin_url: '',
    social_twitter_url: '',
    social_goodReads_url: ''
  })

  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)

  const handleChange = (e) => {
    const { name, value } = e.target
    setFormData({ ...formData, [name]: value })
  }

  const isCustomGender = formData.gender === 'custom'

  const handleSubmit = (e) => {
    e.preventDefault()
    console.log('Form submitted:', formData)
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-blue-50">
      {/* Top Navigation */}
      <div className="bg-white/80 backdrop-blur-md border-b border-gray-200 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            {/* Left Navigation - Hidden on mobile */}
            <div className="hidden lg:flex items-center space-x-8">
              <span className="text-gray-600 hover:text-blue-600 cursor-pointer transition-colors duration-200 font-medium">Romance</span>
              <span className="text-gray-600 hover:text-blue-600 cursor-pointer transition-colors duration-200 font-medium">Fantasy</span>
              <span className="text-gray-600 hover:text-blue-600 cursor-pointer transition-colors duration-200 font-medium">Mystery</span>
              <span className="text-gray-600 hover:text-blue-600 cursor-pointer transition-colors duration-200 font-medium">Browse All</span>
            </div>

            {/* Mobile Menu Button */}
            <button
              className="lg:hidden p-2 rounded-md text-gray-600 hover:text-blue-600 hover:bg-gray-100"
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            >
              {isMobileMenuOpen ? <X size={24} /> : <Menu size={24} />}
            </button>

            {/* Center Logo */}
            <div className="flex items-center">
              <span className="text-xl lg:text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                ✍️ SKRIVEE
              </span>
            </div>

            {/* Right Search - Hidden on mobile */}
            <div className="hidden md:flex items-center space-x-3">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={18} />
                <input
                  type="text"
                  placeholder="Search..."
                  className="w-48 pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                />
              </div>
              <select className="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white">
                <option>Authors</option>
              </select>
            </div>
          </div>

          {/* Mobile Menu */}
          {isMobileMenuOpen && (
            <div className="lg:hidden py-4 border-t border-gray-200">
              <div className="flex flex-col space-y-3">
                <span className="text-gray-600 hover:text-blue-600 cursor-pointer transition-colors duration-200 font-medium">Romance</span>
                <span className="text-gray-600 hover:text-blue-600 cursor-pointer transition-colors duration-200 font-medium">Fantasy</span>
                <span className="text-gray-600 hover:text-blue-600 cursor-pointer transition-colors duration-200 font-medium">Mystery</span>
                <span className="text-gray-600 hover:text-blue-600 cursor-pointer transition-colors duration-200 font-medium">Browse All</span>
                <div className="pt-3 border-t border-gray-200">
                  <div className="relative mb-3">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={18} />
                    <input
                      type="text"
                      placeholder="Search..."
                      className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                  <select className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white">
                    <option>Authors</option>
                  </select>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      <div className="flex flex-col lg:flex-row">
        {/* Left Sidebar - Hidden on mobile, shown as overlay on tablet */}
        <div className="hidden lg:block w-64 bg-white/90 backdrop-blur-md min-h-screen border-r border-gray-200 shadow-lg">
          <div className="p-6">
            {/* Logo */}
            <div className="flex items-center mb-8">
              <img
                src={logo}
                alt="Skrivee Logo"
                className="h-12 w-auto font-bold"
              />
            </div>

            {/* Navigation Menu */}
            <nav className="space-y-2">
              <div className="flex items-center space-x-3 p-3 text-blue-600 bg-gradient-to-r from-blue-50 to-blue-100 rounded-lg shadow-sm border border-blue-200">
                <span>✍️</span>
                <span className="font-medium">Skrivee</span>
              </div>
              <div className="flex items-center space-x-3 p-3 text-gray-600 hover:bg-gradient-to-r hover:from-gray-50 hover:to-gray-100 rounded-lg cursor-pointer transition-all duration-200 hover:shadow-sm">
                <span>👥</span>
                <span>Ranking</span>
              </div>
              <div className="flex items-center space-x-3 p-3 text-gray-600 hover:bg-gradient-to-r hover:from-gray-50 hover:to-gray-100 rounded-lg cursor-pointer transition-all duration-200 hover:shadow-sm">
                <span>⚡</span>
                <span>Pitch</span>
              </div>
              <div className="flex items-center space-x-3 p-3 text-gray-600 hover:bg-gradient-to-r hover:from-gray-50 hover:to-gray-100 rounded-lg cursor-pointer transition-all duration-200 hover:shadow-sm">
                <span>💬</span>
                <span>Messages</span>
              </div>
              <div className="flex items-center space-x-3 p-3 text-gray-600 hover:bg-gradient-to-r hover:from-gray-50 hover:to-gray-100 rounded-lg cursor-pointer transition-all duration-200 hover:shadow-sm">
                <span>🔔</span>
                <span>Notifications</span>
              </div>
              <div className="flex items-center space-x-3 p-3 text-gray-600 hover:bg-gradient-to-r hover:from-gray-50 hover:to-gray-100 rounded-lg cursor-pointer transition-all duration-200 hover:shadow-sm">
                <span>📊</span>
                <span>Dashboard</span>
              </div>
              <div className="flex items-center space-x-3 p-3 text-gray-600 hover:bg-gradient-to-r hover:from-gray-50 hover:to-gray-100 rounded-lg cursor-pointer transition-all duration-200 hover:shadow-sm">
                <span>❓</span>
                <span>Help</span>
              </div>
              <div className="flex items-center space-x-3 p-3 text-gray-600 hover:bg-gradient-to-r hover:from-gray-50 hover:to-gray-100 rounded-lg cursor-pointer transition-all duration-200 hover:shadow-sm">
                <span>⚙️</span>
                <span>Settings</span>
              </div>
            </nav>

            {/* Profile Section */}
            <div className="mt-8 pt-6 border-t border-gray-200">
              <div className="flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-50 transition-all duration-200">
                <div className="w-10 h-10 bg-gradient-to-r from-blue-400 to-purple-500 rounded-full flex items-center justify-center">
                  <User className="text-white" size={20} />
                </div>
                <span className="text-gray-700 font-medium">Profile</span>
              </div>
              <div className="flex items-center space-x-3 p-3 text-gray-600 hover:bg-red-50 hover:text-red-600 rounded-lg cursor-pointer transition-all duration-200">
                <span>🚪</span>
                <span>Logout</span>
              </div>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="flex-1 w-full px-4 mb-10">
          <div className="max-w-6xl mx-auto">
            {/* Header */}
            <div className="mb-8">
              <h1 className="text-2xl sm:text-3xl font-bold text-gray-800 mb-2">Edit Profile, Ravi Agarwal</h1>
              <p className="text-gray-600">Author</p>
              <div className="w-20 h-1 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full mt-2"></div>
            </div>

            <form className="space-y-6 mt-8 bg-white shadow-md rounded-xl md:p-10 md:px-10 p-4" onSubmit={handleSubmit}>
              {/* Full Name & Username */}
              <div className="md:flex-row flex-col flex gap-6">
                <div className="w-full md:w-1/2">
                  <label className="block font-medium">Full Name <span className="text-red-500">*</span></label>
                  <input
                    type="text"
                    autoComplete='off'
                    name="fullName"
                    value={formData.fullName}
                    onChange={handleChange}
                    className="input w-full border focus:outline-none focus:ring-2 focus:ring-[#4A99F8]"
                    required
                  />
                </div>
                <div className="w-full md:w-1/2">
                  <label className="block font-medium">Username <span className="text-red-500">*</span></label>
                  <input
                    type="text"
                    autoComplete='off'
                    name="userName"
                    value={formData.userName}
                    onChange={handleChange}
                    className="input w-full border focus:outline-none focus:ring-2 focus:ring-[#4A99F8]"
                    required
                  />
                </div>
              </div>

              {/* Bio */}
              <div>
                <label className="block font-medium">Bio <span className="text-red-500">*</span></label>
                <textarea
                  autoComplete='off'
                  name="bio"
                  value={formData.bio}
                  onChange={handleChange}
                  maxLength={150}
                  className={`textarea w-full border focus:outline-none focus:ring-2 ${formData.bio.length > 150 ? 'ring-red-500' : 'focus:ring-[#4A99F8]'}`}
                  rows={4}
                ></textarea>
                <div className={`text-sm text-right ${formData.bio.length >= 150 ? 'text-red-500' : 'text-gray-400'}`}>
                  {formData.bio.length}/150
                </div>
              </div>

              {/* Passion */}
              <div>
                <label className="block font-medium">Passion</label>
                <textarea
                  name="passion"
                  autoComplete='off'
                  value={formData.passion}
                  onChange={handleChange}
                  maxLength={100}
                  className="textarea w-full border focus:outline-none focus:ring-2 focus:ring-[#4A99F8]"
                  rows={2}
                ></textarea>
                <div className={`text-sm text-right ${formData.passion.length >= 100 ? 'text-red-500' : 'text-gray-400'}`}>
                  {formData.passion.length}/100
                </div>
              </div>

              {/* Gender Selection */}
              <div className=''>
                <label className="block font-medium mb-2">Gender <span className="text-red-500">*</span></label>
                <div className="flex items-center gap-6 flex-wrap">
                  <label className="flex items-center gap-2">
                    <input type="radio" name="gender" value="male" checked={formData.gender === 'male'} onChange={handleChange} /> Male
                  </label>
                  <label className="flex items-center gap-2">
                    <input type="radio" name="gender" value="female" checked={formData.gender === 'female'} onChange={handleChange} /> Female
                  </label>
                  <label className="flex items-center gap-2">
                    <input type="radio" name="gender" value="custom" checked={formData.gender === 'custom'} onChange={handleChange} /> Custom
                  </label>
                  <input
                    type="text"
                    autoComplete='off'
                    name="customGender"
                    placeholder="Specify..."
                    value={formData.customGender}
                    onChange={handleChange}
                    disabled={!isCustomGender}
                    className="input border focus:outline-none focus:ring-2 focus:ring-[#4A99F8] disabled:opacity-50"
                  />
                </div>
              </div>

              {/* DOB & Occupation */}
              <div className="flex gap-6">
                <div className="flex-1 w-1/2">
                  <label className="block font-medium">Date of Birth <span className="text-red-500">*</span></label>
                  <input type="date" name="dob" value={formData.dob} onChange={handleChange}
                    className="input w-full border focus:outline-none focus:ring-2 focus:ring-[#4A99F8]" required />
                </div>
                <div className="flex-1 w-1/2">
                  <label className="block font-medium">Occupation</label>
                  <input type="text" autoComplete='off'
                    name="occupation" value={formData.occupation} onChange={handleChange}
                    className="input w-full border focus:outline-none focus:ring-2 focus:ring-[#4A99F8]" />
                </div>
              </div>

              {/* Location & Mobile */}
              <div className="flex flex-col gap-6">
                <div className="flex-1">
                  <label className="block font-medium">Location <span className="text-red-500">*</span></label>
                  <input type="text" autoComplete='off' name="location" value={formData.location} onChange={handleChange}
                    className="input w-full border focus:outline-none focus:ring-2 focus:ring-[#4A99F8]" required />
                </div>
                <div className="flex-1">
                  <label className="block font-medium">
                    Mobile Number <span className="text-red-500">*</span>
                  </label>
                  <input type="tel" autoComplete='off' name="phone" value={formData.phone} onChange={handleChange}
                    className="input w-full border focus:outline-none focus:ring-2 focus:ring-[#4A99F8]" required />
                </div>
              </div>

              {/* Language & Email */}
              <div className="flex gap-6 md:flex-row flex-col">
                <div className="flex-1">
                  <label className="block font-medium">Language</label>
                  <input type="text" name="language" value={formData.language} onChange={handleChange}
                    className="input w-full border focus:outline-none focus:ring-2 focus:ring-[#4A99F8]" />
                </div>
                <div className="flex-1">
                  <label className="block font-medium">Email ID <span className="text-red-500">*</span></label>
                  <div className="bg-white p-3 border border-gray-300 h-10 rounded text-sm font-medium flex items-center text-gray-700 mb-4"><EMAIL></div>
                </div>
              </div>

              {/* Save Button */}
              <div className="flex justify-center items-center">
                <div className="flex items-center justify-center mt-4 gap-2">
                  <div className="md:w-32 transform -translate-y-2 w-18 h-1 rounded-l-full bg-[#4A99F8]" />
                  <button
                    type="submit"
                    className="relative transform -translate-y-2 bg-[#4A99F8] hover:bg-blue-600 text-white font-medium py-2 px-6 rounded-md transition-all"
                  >
                    Save
                  </button>
                  <div className="md:w-32 w-18 h-1 rounded-r-full transform -translate-y-2 bg-[#D99FE9]" />
                </div>
              </div>
            </form>
          </div>
        </div>

      </div>
    </div>
  )
}

export default Authors
